import axios, { AxiosInstance } from 'axios';
import { Config, LLMResponse, Message, ArienError } from '../types/index.js';
import { RetryManager } from './retry-logic.js';

export abstract class LLMProvider {
  protected config: Config;
  protected retryManager: RetryManager;

  constructor(config: Config) {
    this.config = config;
    this.retryManager = new RetryManager();
  }

  abstract generateResponse(messages: Message[]): Promise<LLMResponse>;
  abstract validateConnection(): Promise<boolean>;
}

export class DeepseekProvider extends LLMProvider {
  private client: AxiosInstance;

  constructor(config: Config) {
    super(config);
    
    if (!config.apiKey) {
      throw new ArienError('Deepseek API key is required', 'MISSING_API_KEY');
    }

    this.client = axios.create({
      baseURL: 'https://api.deepseek.com/v1',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: config.timeout
    });
  }

  async generateResponse(messages: Message[]): Promise<LLMResponse> {
    return this.retryManager.executeWithRateLimit(async () => {
      const formattedMessages = this.formatMessages(messages);

      // Build request payload based on model type
      const requestPayload: any = {
        model: this.config.model,
        messages: formattedMessages,
        tools: this.getToolDefinitions(),
        tool_choice: 'auto',
        stream: false
      };

      // deepseek-reasoner doesn't support temperature and other parameters
      if (this.config.model !== 'deepseek-reasoner') {
        requestPayload.temperature = 0.7;
      }

      // Set max_tokens appropriately for each model
      if (this.config.model === 'deepseek-reasoner') {
        requestPayload.max_tokens = 32000; // Default for reasoner model
      } else {
        requestPayload.max_tokens = 4000;
      }

      try {
        const response = await this.client.post('/chat/completions', requestPayload);
        return this.parseResponse(response.data);
      } catch (error: any) {
        // Enhanced error logging for debugging
        if (error.response) {
          const errorDetails = {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data,
            requestPayload: {
              ...requestPayload,
              messages: formattedMessages.length > 0 ?
                `[${formattedMessages.length} messages]` : 'no messages'
            }
          };
          console.error('Deepseek API Error:', JSON.stringify(errorDetails, null, 2));

          // Create a more descriptive error message
          const errorMessage = error.response.data?.error?.message ||
                              error.response.data?.message ||
                              `HTTP ${error.response.status}: ${error.response.statusText}`;
          throw new ArienError(
            `Deepseek API Error (${error.response.status}): ${errorMessage}`,
            'API_ERROR',
            false
          );
        } else if (error.code === 'ECONNABORTED' || error.message.includes('aborted') || error.message.includes('timeout')) {
          // Handle aborted/timeout errors as retryable
          throw new ArienError(
            `Request aborted or timed out: ${error.message}`,
            'TIMEOUT_ERROR',
            true // Retryable
          );
        }
        throw error;
      }
    });
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.client.get('/models');
      return true;
    } catch (error) {
      return false;
    }
  }

  private formatMessages(messages: Message[]): any[] {
    return messages.map(msg => {
      const formatted: any = {
        role: msg.role,
        content: msg.content
      };

      if (msg.toolCalls && msg.toolCalls.length > 0) {
        formatted.tool_calls = msg.toolCalls.map(call => ({
          id: call.id,
          type: 'function',
          function: {
            name: call.type,
            arguments: JSON.stringify(call.parameters)
          }
        }));
      }

      if (msg.role === 'tool' && msg.toolCallId) {
        formatted.tool_call_id = msg.toolCallId;
      }

      return formatted;
    });
  }

  private parseResponse(data: any): LLMResponse {
    const choice = data.choices[0];
    const message = choice.message;

    const response: LLMResponse = {
      content: message.content || ''
    };

    // Handle reasoning content for deepseek-reasoner model
    if (message.reasoning_content) {
      response.reasoningContent = message.reasoning_content;
    }

    if (data.usage) {
      response.usage = {
        promptTokens: data.usage.prompt_tokens || 0,
        completionTokens: data.usage.completion_tokens || 0,
        totalTokens: data.usage.total_tokens || 0
      };
    }

    if (message.tool_calls && message.tool_calls.length > 0) {
      response.toolCalls = message.tool_calls.map((call: any) => ({
        id: call.id,
        type: call.function.name,
        parameters: JSON.parse(call.function.arguments)
      }));
    }

    return response;
  }

  private getToolDefinitions(): any[] {
    return [
      {
        type: 'function',
        function: {
          name: 'bash',
          description: 'Execute bash commands in an interactive shell',
          parameters: {
            type: 'object',
            properties: {
              command: {
                type: 'string',
                description: 'The bash command to execute'
              },
              workingDirectory: {
                type: 'string',
                description: 'Optional working directory for the command'
              }
            },
            required: ['command']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'grep',
          description: 'Search for text patterns within files',
          parameters: {
            type: 'object',
            properties: {
              pattern: {
                type: 'string',
                description: 'Text or regex pattern to search for'
              },
              path: {
                type: 'string',
                description: 'File or directory path to search in'
              },
              recursive: {
                type: 'boolean',
                description: 'Search recursively in subdirectories',
                default: true
              },
              ignoreCase: {
                type: 'boolean',
                description: 'Case-insensitive search',
                default: false
              }
            },
            required: ['pattern', 'path']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'glob',
          description: 'Find files matching specific patterns',
          parameters: {
            type: 'object',
            properties: {
              pattern: {
                type: 'string',
                description: 'Glob pattern to match files'
              },
              cwd: {
                type: 'string',
                description: 'Working directory for the search'
              }
            },
            required: ['pattern']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'write',
          description: 'Create or overwrite files with content',
          parameters: {
            type: 'object',
            properties: {
              path: {
                type: 'string',
                description: 'File path to write to'
              },
              content: {
                type: 'string',
                description: 'Content to write to the file'
              }
            },
            required: ['path', 'content']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'edit',
          description: 'Edit existing files by replacing text sections',
          parameters: {
            type: 'object',
            properties: {
              path: {
                type: 'string',
                description: 'File path to edit'
              },
              search: {
                type: 'string',
                description: 'Text to search for and replace'
              },
              replace: {
                type: 'string',
                description: 'Replacement text'
              },
              global: {
                type: 'boolean',
                description: 'Replace all occurrences',
                default: false
              }
            },
            required: ['path', 'search', 'replace']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'web',
          description: 'Fetch real-time information from the internet',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Search query or URL to fetch'
              },
              maxResults: {
                type: 'number',
                description: 'Maximum number of results',
                default: 5
              }
            },
            required: ['query']
          }
        }
      }
    ];
  }
}

export class OllamaProvider extends LLMProvider {
  private client: AxiosInstance;

  constructor(config: Config) {
    super(config);

    this.client = axios.create({
      baseURL: config.ollamaUrl,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: config.timeout
    });
  }

  async generateResponse(messages: Message[]): Promise<LLMResponse> {
    return this.retryManager.executeWithNetworkRetry(async () => {
      try {
        const formattedMessages = this.formatMessages(messages);

        const response = await this.client.post('/api/chat', {
          model: this.config.model,
          messages: formattedMessages,
          tools: this.getToolDefinitions(),
          stream: false,
          options: {
            temperature: 0.7,
            num_predict: 4000
          }
        });

        return this.parseResponse(response.data);
      } catch (error: any) {
        // Handle Ollama-specific errors
        if (error.response) {
          // HTTP error response from Ollama
          const errorDetails = {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data
          };
          console.error('Ollama API Error:', JSON.stringify(errorDetails, null, 2));

          // Create a more descriptive error message
          const errorMessage = error.response.data?.error ||
                              error.response.data?.message ||
                              `HTTP ${error.response.status}: ${error.response.statusText}`;
          throw new ArienError(
            `Ollama API Error (${error.response.status}): ${errorMessage}`,
            'API_ERROR',
            false
          );
        } else if (error.code === 'ECONNABORTED' || error.message.includes('aborted') || error.message.includes('timeout')) {
          // Handle aborted/timeout errors as retryable
          throw new ArienError(
            `Request aborted or timed out: ${error.message}`,
            'TIMEOUT_ERROR',
            true // Retryable
          );
        }
        throw error;
      }
    });
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.client.get('/api/tags');
      return true;
    } catch (error) {
      return false;
    }
  }

  private formatMessages(messages: Message[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      tool_calls: msg.toolCalls?.map(call => ({
        id: call.id,
        type: 'function',
        function: {
          name: call.type,
          arguments: call.parameters
        }
      }))
    }));
  }

  private parseResponse(data: any): LLMResponse {
    const response: LLMResponse = {
      content: data.message?.content || ''
    };

    if (data.message?.tool_calls && data.message.tool_calls.length > 0) {
      response.toolCalls = data.message.tool_calls.map((call: any) => ({
        id: call.id,
        type: call.function.name,
        parameters: call.function.arguments
      }));
    }

    return response;
  }

  private getToolDefinitions(): any[] {
    // Ollama uses the same tool definitions as Deepseek
    // We need to create a temporary instance to get the definitions
    const tempConfig = { ...this.config, apiKey: 'temp' };
    const tempProvider = new DeepseekProvider(tempConfig);
    return (tempProvider as any).getToolDefinitions();
  }
}

export function createLLMProvider(config: Config): LLMProvider {
  switch (config.provider) {
    case 'deepseek':
      return new DeepseekProvider(config);
    case 'ollama':
      return new OllamaProvider(config);
    default:
      throw new ArienError(`Unsupported LLM provider: ${config.provider}`, 'INVALID_PROVIDER');
  }
}
