import { z } from 'zod';

// LLM Provider Types
export const LLMProviderSchema = z.enum(['deepseek', 'ollama']);
export type LLMProvider = z.infer<typeof LLMProviderSchema>;

export const DeepseekModelSchema = z.enum(['deepseek-chat', 'deepseek-reasoner']);
export type DeepseekModel = z.infer<typeof DeepseekModelSchema>;

// Configuration Types
export const ConfigSchema = z.object({
  provider: LLMProviderSchema,
  apiKey: z.string().optional(),
  model: z.string(),
  ollamaUrl: z.string().default('http://localhost:11434'),
  maxRetries: z.number().default(3),
  retryDelay: z.number().default(1000),
  timeout: z.number().default(60000),
});

export type Config = z.infer<typeof ConfigSchema>;

// Tool Types
export const ToolTypeSchema = z.enum(['bash', 'grep', 'glob', 'write', 'edit', 'web']);
export type ToolType = z.infer<typeof ToolTypeSchema>;

export interface ToolCall {
  id: string;
  type: ToolType;
  parameters: Record<string, any>;
}

export interface ToolResult {
  id: string;
  success: boolean;
  output: string;
  error?: string | undefined;
  executionTime: number;
}

// Message Types
export interface Message {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCalls?: ToolCall[] | undefined;
  toolResults?: ToolResult[] | undefined;
  toolCallId?: string | undefined; // For tool response messages
  timestamp: Date;
}

// LLM Response Types
export interface LLMResponse {
  content: string;
  toolCalls?: ToolCall[];
  reasoningContent?: string; // For deepseek-reasoner model
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

// Error Types
export class ArienError extends Error {
  constructor(
    message: string,
    public code: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'ArienError';
  }
}

// Animation Types
export interface AnimationFrame {
  frame: string;
  duration: number;
}

// Tool Execution Types
export interface ExecutionContext {
  workingDirectory: string;
  environment: Record<string, string>;
  timeout: number;
}

// Retry Logic Types
export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  exponentialBase: number;
  jitter: boolean;
}

export interface RetryState {
  attempt: number;
  lastError?: Error;
  totalElapsed: number;
}
