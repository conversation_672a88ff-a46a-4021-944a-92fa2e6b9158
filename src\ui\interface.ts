import chalk from 'chalk';
import inquirer from 'inquirer';
import { AnimationManager } from './animations.js';
import { Config, LL<PERSON>rovider } from '../types/index.js';

export class UserInterface {
  private animation: AnimationManager;

  constructor() {
    this.animation = new AnimationManager();
  }

  showWelcome(): void {
    console.clear();
    console.log(chalk.cyan.bold(`
    ╔═══════════════════════════════════════╗
    ║           🤖 ARIEN AI CLI             ║
    ║     Advanced AI Terminal Assistant    ║
    ╚═══════════════════════════════════════╝
    `));
    console.log(chalk.gray('Powered by LLM Function Calling & Never Give Up Logic\n'));
  }

  async promptInitialSetup(): Promise<Config> {
    console.log(chalk.yellow('🔧 Initial Setup Required\n'));

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select your LLM provider:',
        choices: [
          { name: '🧠 Deepseek (deepseek-chat, deepseek-reasoner)', value: 'deepseek' },
          { name: '🦙 Ollama (Local models)', value: 'ollama' }
        ]
      }
    ]);

    if (provider === 'deepseek') {
      return await this.setupDeepseek();
    } else {
      return await this.setupOllama();
    }
  }

  private async setupDeepseek(): Promise<Config> {
    const answers = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: 'Enter your Deepseek API key:',
        mask: '*',
        validate: (input: string) => input.length > 0 || 'API key is required'
      },
      {
        type: 'list',
        name: 'model',
        message: 'Select Deepseek model:',
        choices: [
          { name: 'deepseek-chat (Fast, general purpose)', value: 'deepseek-chat' },
          { name: 'deepseek-reasoner (Advanced reasoning)', value: 'deepseek-reasoner' }
        ]
      }
    ]);

    return {
      provider: 'deepseek' as LLMProvider,
      apiKey: answers.apiKey,
      model: answers.model,
      ollamaUrl: 'http://localhost:11434',
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000
    };
  }

  private async setupOllama(): Promise<Config> {
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'ollamaUrl',
        message: 'Ollama URL:',
        default: 'http://localhost:11434',
        validate: (input: string) => {
          try {
            new URL(input);
            return true;
          } catch {
            return 'Please enter a valid URL';
          }
        }
      },
      {
        type: 'input',
        name: 'model',
        message: 'Model name (e.g., llama3.2, codellama, mistral):',
        validate: (input: string) => input.length > 0 || 'Model name is required'
      }
    ]);

    return {
      provider: 'ollama' as LLMProvider,
      model: answers.model,
      ollamaUrl: answers.ollamaUrl,
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000
    };
  }

  async promptUserInput(): Promise<string> {
    const { input } = await inquirer.prompt([
      {
        type: 'input',
        name: 'input',
        message: chalk.cyan('You:'),
        validate: (input: string) => input.trim().length > 0 || 'Please enter a message'
      }
    ]);

    return input.trim();
  }

  async confirmAction(message: string): Promise<boolean> {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: chalk.yellow(`⚠️  ${message}`),
        default: false
      }
    ]);

    return confirm;
  }

  showAIResponse(content: string): void {
    console.log(`\n${chalk.green('🤖 Arien AI:')} ${content}\n`);
  }

  showToolExecution(toolType: string, parameters: any): void {
    // Show tool execution without revealing sensitive parameters
    const sanitizedParams = this.sanitizeParameters(parameters);
    const paramStr = Object.keys(sanitizedParams).length > 0
      ? chalk.gray(`(${Object.keys(sanitizedParams).join(', ')})`)
      : '';

    console.log(`${chalk.blue('🔧 Executing:')} ${chalk.cyan(toolType)} ${paramStr}`);
  }

  showToolResult(toolType: string, success: boolean, output: string, executionTime: number): void {
    const status = success ? chalk.green('✓') : chalk.red('✗');
    const time = chalk.gray(`(${executionTime}ms)`);

    console.log(`${status} ${chalk.cyan(toolType)} completed ${time}`);

    // Only show output summary, not full output (as per requirements)
    if (output && output.trim()) {
      const summary = this.generateOutputSummary(output, success);
      if (summary) {
        console.log(chalk.gray(`   ${summary}`));
      }
    }
    console.log();
  }

  private sanitizeParameters(params: any): any {
    const sanitized = { ...params };

    // Remove or mask sensitive information
    const sensitiveKeys = ['password', 'apiKey', 'token', 'secret', 'key'];
    for (const key of sensitiveKeys) {
      if (sanitized[key]) {
        sanitized[key] = '***';
      }
    }

    // Truncate long values
    for (const [key, value] of Object.entries(sanitized)) {
      if (typeof value === 'string' && value.length > 50) {
        sanitized[key] = value.substring(0, 47) + '...';
      }
    }

    return sanitized;
  }

  private generateOutputSummary(output: string, success: boolean): string {
    if (!success) {
      // For errors, show first line of error message
      const firstLine = output.split('\n')[0] || 'Unknown error';
      return `Error: ${firstLine.substring(0, 100)}${firstLine.length > 100 ? '...' : ''}`;
    }

    // For successful operations, provide meaningful summaries
    const lines = output.split('\n').filter(line => line.trim());

    if (lines.length === 0) {
      return 'Operation completed successfully';
    }

    if (lines.length === 1) {
      const line = lines[0] || '';
      return line.substring(0, 100) + (line.length > 100 ? '...' : '');
    }

    // Multi-line output summary
    if (output.includes('STDOUT:') || output.includes('STDERR:')) {
      return `Command output: ${lines.length} lines`;
    }

    if (output.includes('files found') || output.includes('matches')) {
      return `Search completed: ${lines.length} results`;
    }

    return `Operation completed: ${lines.length} lines of output`;
  }

  showError(error: string): void {
    this.animation.showError(error);
  }

  showWarning(warning: string): void {
    this.animation.showWarning(warning);
  }

  showInfo(info: string): void {
    this.animation.showInfo(info);
  }

  showSuccess(message: string): void {
    this.animation.showSuccess(message);
  }

  startThinking(message = 'Processing your request...'): void {
    this.animation.start(message);
  }

  startProgress(message = 'Working...'): void {
    this.animation.start(message);
  }

  stopAnimation(success: boolean = true): void {
    this.animation.stop(success);
  }

  showProgress(current: number, total: number, message = ''): void {
    this.animation.showProgress(current, total, message);
  }

  showStep(step: number, total: number, message: string): void {
    this.animation.showStep(step, total, message);
  }

  clearLine(): void {
    this.animation.clearLine();
  }

  newLine(): void {
    this.animation.newLine();
  }

  showConfigStatus(config: Config): void {
    console.log(chalk.blue('\n📋 Current Configuration:'));
    console.log(`   Provider: ${chalk.cyan(config.provider)}`);
    console.log(`   Model: ${chalk.cyan(config.model)}`);

    if (config.provider === 'deepseek') {
      console.log(`   API Key: ${chalk.gray(config.apiKey ? '***configured***' : 'not set')}`);
    } else {
      console.log(`   Ollama URL: ${chalk.cyan(config.ollamaUrl)}`);
    }

    console.log(`   Max Retries: ${chalk.cyan(config.maxRetries)}`);
    console.log(`   Timeout: ${chalk.cyan(config.timeout)}ms\n`);
  }

  // Enhanced real-time interface methods
  showThinkingProcess(message: string): void {
    console.log(`${chalk.yellow('🧠 Thinking:')} ${chalk.gray(message)}`);
  }

  showPlanningPhase(steps: string[]): void {
    console.log(`${chalk.blue('📋 Planning:')} ${chalk.gray('Breaking down your request...')}`);
    steps.forEach((step, index) => {
      console.log(`   ${chalk.cyan(`${index + 1}.`)} ${step}`);
    });
    console.log();
  }

  showExecutionPhase(currentStep: number, totalSteps: number, stepDescription: string): void {
    const progress = `[${currentStep}/${totalSteps}]`;
    console.log(`${chalk.green('⚡ Executing:')} ${chalk.cyan(progress)} ${stepDescription}`);
  }

  showRetryAttempt(attempt: number, maxAttempts: number, reason: string): void {
    console.log(`${chalk.yellow('🔄 Retry:')} ${chalk.gray(`Attempt ${attempt}/${maxAttempts} - ${reason}`)}`);
  }

  showAlternativeStrategy(strategy: string): void {
    console.log(`${chalk.magenta('🔀 Alternative:')} ${chalk.gray(strategy)}`);
  }

  showProgressUpdate(message: string, percentage?: number): void {
    const percentStr = percentage !== undefined ? chalk.cyan(`${percentage}%`) : '';
    console.log(`${chalk.blue('📊 Progress:')} ${message} ${percentStr}`);
  }

  showStreamingResponse(chunk: string): void {
    // For streaming AI responses
    process.stdout.write(chunk);
  }

  showToolOutputSummary(toolType: string, summary: string): void {
    console.log(`${chalk.gray('   →')} ${chalk.cyan(toolType)}: ${summary}`);
  }

  showDecisionRationale(decision: string, reasoning: string): void {
    console.log(`${chalk.blue('🤔 Decision:')} ${decision}`);
    console.log(`${chalk.gray('   Reasoning:')} ${reasoning}`);
  }

  showResourceUsage(stats: { memory?: number; cpu?: number; time?: number }): void {
    const parts: string[] = [];
    if (stats.memory) parts.push(`Memory: ${stats.memory}MB`);
    if (stats.cpu) parts.push(`CPU: ${stats.cpu}%`);
    if (stats.time) parts.push(`Time: ${stats.time}ms`);

    if (parts.length > 0) {
      console.log(`${chalk.gray('📈 Resources:')} ${parts.join(' | ')}`);
    }
  }

  showCompletionSummary(totalSteps: number, successfulSteps: number, totalTime: number): void {
    const successRate = Math.round((successfulSteps / totalSteps) * 100);
    const timeStr = totalTime > 1000 ? `${(totalTime / 1000).toFixed(1)}s` : `${totalTime}ms`;

    console.log(`\n${chalk.green('✅ Completed:')} ${successfulSteps}/${totalSteps} steps (${successRate}%) in ${timeStr}`);
  }

  showInteractivePrompt(message: string, options?: string[]): void {
    console.log(`\n${chalk.yellow('❓ Input needed:')} ${message}`);
    if (options && options.length > 0) {
      options.forEach((option, index) => {
        console.log(`   ${chalk.cyan(`${index + 1}.`)} ${option}`);
      });
    }
  }

  clearCurrentLine(): void {
    process.stdout.write('\r\x1b[K');
  }

  moveCursorUp(lines: number = 1): void {
    process.stdout.write(`\x1b[${lines}A`);
  }

  moveCursorDown(lines: number = 1): void {
    process.stdout.write(`\x1b[${lines}B`);
  }

  saveCurrentPosition(): void {
    process.stdout.write('\x1b[s');
  }

  restorePosition(): void {
    process.stdout.write('\x1b[u');
  }
}
