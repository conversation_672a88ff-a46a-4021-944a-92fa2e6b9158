import axios, { AxiosInstance } from 'axios';
import * as cheerio from 'cheerio';
import { ToolResult } from '../types/index.js';
import { nanoid } from 'nanoid';

export interface WebToolParams {
  query: string;
  maxResults?: number;
  timeout?: number;
}

export class WebTool {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      timeout: 45000, // Increased timeout for web requests
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
  }

  async execute(params: WebToolParams): Promise<ToolResult> {
    const id = nanoid();
    const startTime = Date.now();

    try {
      const results = await this.searchWeb(params);
      const output = this.formatResults(results, params.query);

      return {
        id,
        success: true,
        output,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        id,
        success: false,
        output: '',
        error: error instanceof Error ? error.message : String(error),
        executionTime: Date.now() - startTime
      };
    }
  }

  private async searchWeb(params: WebToolParams): Promise<SearchResult[]> {
    const maxResults = params.maxResults || 5;
    
    try {
      // Use DuckDuckGo Instant Answer API (free and doesn't require API key)
      const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(params.query)}&format=json&no_html=1&skip_disambig=1`;
      
      const response = await this.client.get(searchUrl, {
        timeout: params.timeout || 30000 // Increased individual request timeout
      });

      const data = response.data;
      const results: SearchResult[] = [];

      // Process instant answer
      if (data.Abstract && data.Abstract.trim()) {
        results.push({
          title: data.Heading || 'Instant Answer',
          url: data.AbstractURL || '',
          snippet: data.Abstract,
          source: data.AbstractSource || 'DuckDuckGo'
        });
      }

      // Process related topics
      if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
        data.RelatedTopics.slice(0, maxResults - results.length).forEach((topic: any) => {
          if (topic.Text && topic.FirstURL) {
            results.push({
              title: this.extractTitle(topic.Text),
              url: topic.FirstURL,
              snippet: topic.Text,
              source: 'DuckDuckGo'
            });
          }
        });
      }

      // If we don't have enough results, try to get more from the answer
      if (results.length < maxResults && data.Answer) {
        results.push({
          title: 'Direct Answer',
          url: '',
          snippet: data.Answer,
          source: 'DuckDuckGo'
        });
      }

      // If still no results, try a different approach with HTML search
      if (results.length === 0) {
        return await this.fallbackSearch(params.query, maxResults);
      }

      return results.slice(0, maxResults);

    } catch (error) {
      // Fallback to HTML search if API fails
      return await this.fallbackSearch(params.query, maxResults);
    }
  }

  private async fallbackSearch(query: string, maxResults: number): Promise<SearchResult[]> {
    try {
      // Use DuckDuckGo HTML search as fallback
      const searchUrl = `https://html.duckduckgo.com/html/?q=${encodeURIComponent(query)}`;
      
      const response = await this.client.get(searchUrl, {
        timeout: 20000 // Increased fallback timeout
      });

      const $ = cheerio.load(response.data);
      const results: SearchResult[] = [];

      $('.result').each((index: number, element: any) => {
        if (index >= maxResults) return false;

        const $element = $(element);
        const title = $element.find('.result__title a').text().trim();
        const url = $element.find('.result__title a').attr('href') || '';
        const snippet = $element.find('.result__snippet').text().trim();

        if (title && snippet) {
          results.push({
            title,
            url: url.startsWith('//') ? `https:${url}` : url,
            snippet,
            source: 'DuckDuckGo'
          });
        }

        return true; // Continue iteration
      });

      return results;

    } catch (error) {
      // If all else fails, return a helpful message
      return [{
        title: 'Search Information',
        url: '',
        snippet: `Unable to fetch real-time search results for "${query}". This might be due to network restrictions or rate limiting. You can try searching manually at https://duckduckgo.com/?q=${encodeURIComponent(query)}`,
        source: 'System'
      }];
    }
  }

  private extractTitle(text: string): string {
    // Extract a reasonable title from the text
    const sentences = text.split(/[.!?]/);
    const firstSentence = sentences[0]?.trim();
    
    if (firstSentence && firstSentence.length > 10 && firstSentence.length < 100) {
      return firstSentence;
    }
    
    // Fallback to first 50 characters
    return text.length > 50 ? text.substring(0, 47) + '...' : text;
  }

  private formatResults(results: SearchResult[], query: string): string {
    if (results.length === 0) {
      return `No search results found for: ${query}`;
    }

    const output: string[] = [];
    output.push(`🔍 Search results for: ${query}\n`);

    results.forEach((result, index) => {
      output.push(`${index + 1}. **${result.title}**`);
      
      if (result.url) {
        output.push(`   🔗 ${result.url}`);
      }
      
      if (result.snippet) {
        // Clean up the snippet
        const cleanSnippet = result.snippet
          .replace(/\s+/g, ' ')
          .trim()
          .substring(0, 300);
        
        output.push(`   📄 ${cleanSnippet}${result.snippet.length > 300 ? '...' : ''}`);
      }
      
      output.push(`   📊 Source: ${result.source}`);
      output.push('');
    });

    return output.join('\n');
  }

  getDescription(): string {
    return `Fetch real-time information from the internet using DuckDuckGo search.
    
Usage:
- Getting current information, news, or data
- API documentation lookup
- Technology research and comparisons
- Troubleshooting with latest solutions

Examples:
- "TypeScript 5.8 new features"
- "Node.js 22 best practices"
- "How to fix ECONNRESET error"
- "Latest React hooks patterns"

Features:
- Uses DuckDuckGo API for privacy-focused search
- Returns structured results with titles, URLs, and snippets
- Automatic fallback to HTML parsing if API fails
- Respects rate limits and handles network errors gracefully

Notes:
- Results are returned in markdown format
- Limited to prevent overwhelming output
- May be subject to rate limiting on heavy usage
- Does not store or cache search results`;
  }

  validateParams(params: any): params is WebToolParams {
    return (
      typeof params === 'object' &&
      typeof params.query === 'string' &&
      params.query.trim().length > 0 &&
      (params.maxResults === undefined || typeof params.maxResults === 'number') &&
      (params.timeout === undefined || typeof params.timeout === 'number')
    );
  }

  // Utility method to fetch a specific URL
  async fetchUrl(url: string): Promise<{ content: string; title?: string }> {
    try {
      const response = await this.client.get(url, {
        timeout: 20000 // Increased URL fetch timeout
      });

      const $ = cheerio.load(response.data);
      
      // Extract title
      const title = $('title').text().trim() || 
                   $('h1').first().text().trim() || 
                   'Untitled';

      // Extract main content (simplified)
      const content = $('body').text()
        .replace(/\s+/g, ' ')
        .trim()
        .substring(0, 2000);

      return { content, title };

    } catch (error) {
      throw new Error(`Failed to fetch URL: ${error}`);
    }
  }
}

interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
}
