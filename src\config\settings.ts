import Conf from 'conf';
import { Config, ConfigSchema } from '../types/index.js';

export class Settings {
  private config: Conf<Config>;

  constructor() {
    this.config = new Conf<Config>({
      projectName: 'arien-ai-cli',
      schema: {
        provider: {
          type: 'string',
          enum: ['deepseek', 'ollama'],
          default: 'deepseek'
        },
        apiKey: {
          type: 'string'
        },
        model: {
          type: 'string',
          default: 'deepseek-chat'
        },
        ollamaUrl: {
          type: 'string',
          default: 'http://localhost:11434'
        },
        maxRetries: {
          type: 'number',
          default: 3
        },
        retryDelay: {
          type: 'number',
          default: 1000
        },
        timeout: {
          type: 'number',
          default: 60000
        }
      }
    });
  }

  get(): Config {
    const config = this.config.store;
    return ConfigSchema.parse(config);
  }

  set(key: keyof Config, value: any): void {
    this.config.set(key, value);
  }

  setAll(config: Partial<Config>): void {
    const validatedConfig = ConfigSchema.partial().parse(config);
    Object.entries(validatedConfig).forEach(([key, value]) => {
      if (value !== undefined) {
        this.config.set(key as keyof Config, value);
      }
    });
  }

  reset(): void {
    this.config.clear();
  }

  isConfigured(): boolean {
    const config = this.get();
    
    if (config.provider === 'deepseek') {
      return !!(config.apiKey && config.model);
    }
    
    if (config.provider === 'ollama') {
      return !!(config.ollamaUrl && config.model);
    }
    
    return false;
  }

  getConfigPath(): string {
    return this.config.path;
  }

  validate(): { valid: boolean; errors: string[] } {
    try {
      const config = this.get();
      const errors: string[] = [];

      if (config.provider === 'deepseek' && !config.apiKey) {
        errors.push('Deepseek API key is required');
      }

      if (config.provider === 'ollama' && !config.ollamaUrl) {
        errors.push('Ollama URL is required');
      }

      if (!config.model) {
        errors.push('Model name is required');
      }

      return {
        valid: errors.length === 0,
        errors
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`Configuration validation failed: ${error}`]
      };
    }
  }
}
