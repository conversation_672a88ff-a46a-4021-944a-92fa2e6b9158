import chalk from 'chalk';

export class AnimationManager {
  private currentAnimation: NodeJS.Timeout | null = null;
  private frameIndex = 0;
  private startTime = Date.now();

  // Modern enhanced ball animation with smooth motion and visual effects
  private readonly ballFrames = [
    "⟨ ◉     ⟩",  // Start position with enhanced ball
    "⟨  ◉    ⟩",  // Moving right
    "⟨   ◉   ⟩",  // Center
    "⟨    ◉  ⟩",  // Moving right
    "⟨     ◉ ⟩",  // End position
    "⟨    ◉  ⟩",  // Bouncing back
    "⟨   ◉   ⟩",  // Center again
    "⟨  ◉    ⟩",  // Moving left
    "⟨ ◉     ⟩",  // Back to start
    "⟨◉      ⟩",  // Slight overshoot for bounce effect
  ];

  // Color variations for dynamic visual appeal
  private readonly ballColors = [
    chalk.cyan,
    chalk.blue,
    chalk.magenta,
    chalk.green,
    chalk.yellow,
  ];

  private colorIndex = 0;

  private formatElapsedTime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  }

  start(message = ''): void {
    this.stop();
    this.frameIndex = 0;
    this.colorIndex = 0;
    this.startTime = Date.now();

    this.currentAnimation = setInterval(() => {
      const frame = this.ballFrames[this.frameIndex % this.ballFrames.length];
      const elapsed = Math.floor((Date.now() - this.startTime) / 1000);

      // Dynamic color cycling for visual appeal
      const currentColor = this.ballColors[this.colorIndex % this.ballColors.length];

      // Enhanced time display with better formatting
      const timeDisplay = this.formatElapsedTime(elapsed);

      // Modern styled display with enhanced visual elements
      const styledFrame = currentColor ? currentColor(frame) : chalk.cyan(frame);
      const styledTime = chalk.gray.dim(`${timeDisplay}`);
      const styledMessage = message ? chalk.white(message) : '';

      // Add subtle pulsing effect every few seconds
      const pulse = elapsed > 0 && elapsed % 3 === 0 ? chalk.yellow('•') : ' ';

      const display = `${styledFrame} ${styledTime} ${pulse} ${styledMessage}`;

      process.stdout.write(`\r${display}`);

      this.frameIndex++;

      // Change color every 2 frames for smooth color transitions
      if (this.frameIndex % 2 === 0) {
        this.colorIndex++;
      }
    }, 120); // Slightly slower for smoother animation
  }

  stop(success: boolean = true): void {
    if (this.currentAnimation) {
      clearInterval(this.currentAnimation);
      this.currentAnimation = null;

      // Clear the line and show completion indicator
      process.stdout.write('\r\x1b[K');

      // Optional: Show completion time only for successful operations
      const totalTime = Math.floor((Date.now() - this.startTime) / 1000);
      if (totalTime > 0 && success) {
        const completionMsg = `${chalk.green('✓')} ${chalk.gray.dim(`Completed in ${this.formatElapsedTime(totalTime)}`)}`;
        console.log(completionMsg);
      }
    }
  }

  showProgress(current: number, total: number, message = ''): void {
    const percentage = Math.round((current / total) * 100);
    const filled = Math.round((current / total) * 24); // Wider progress bar
    const empty = 24 - filled;

    // Modern progress bar with gradient-like effect
    const filledBar = '▰'.repeat(filled);
    const emptyBar = '▱'.repeat(empty);

    // Color-coded progress bar
    let progressColor = chalk.red;
    if (percentage > 30) progressColor = chalk.yellow;
    if (percentage > 60) progressColor = chalk.blue;
    if (percentage > 80) progressColor = chalk.green;

    const progressBar = progressColor(filledBar) + chalk.gray.dim(emptyBar);
    const percentDisplay = chalk.bold.white(`${percentage}%`);
    const messageDisplay = message ? chalk.gray(message) : '';

    const display = `⟨${progressBar}⟩ ${percentDisplay} ${messageDisplay}`;

    process.stdout.write(`\r${display}`);

    if (current === total) {
      process.stdout.write('\n');
      console.log(chalk.green('✓ Progress completed!'));
    }
  }

  showSuccess(message: string): void {
    console.log(`${chalk.green.bold('✓')} ${chalk.green(message)}`);
  }

  showError(message: string): void {
    console.log(`${chalk.red.bold('✗')} ${chalk.red(message)}`);
  }

  showWarning(message: string): void {
    console.log(`${chalk.yellow.bold('!')} ${chalk.yellow(message)}`);
  }

  showInfo(message: string): void {
    console.log(`${chalk.blue.bold('i')} ${chalk.blue(message)}`);
  }

  showStep(step: number, total: number, message: string): void {
    const progress = `⟨${step}/${total}⟩`;
    const percentage = Math.round((step / total) * 100);
    const progressColor = percentage === 100 ? chalk.green : chalk.cyan;
    console.log(`${progressColor.bold(progress)} ${chalk.white(message)} ${chalk.gray.dim(`${percentage}%`)}`);
  }

  clearLine(): void {
    process.stdout.write('\r\x1b[K');
  }

  newLine(): void {
    console.log();
  }
}
